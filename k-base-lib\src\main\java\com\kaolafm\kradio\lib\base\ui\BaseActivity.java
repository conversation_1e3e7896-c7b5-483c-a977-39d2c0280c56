package com.kaolafm.kradio.lib.base.ui;

import androidx.lifecycle.LifecycleOwner;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.arouter.ARouterBaseActivity;
import com.kaolafm.kradio.lib.base.flavor.ConfigChangeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioActivityLifecyleInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogActivityInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioVehicleSafetyCallback;
import com.kaolafm.kradio.lib.base.flavor.KRadioVehicleSafetyInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioWindowFlagInter;
import com.kaolafm.kradio.lib.base.lifecycle.ActivityLifecycleable;
import com.kaolafm.kradio.lib.base.mvp.IActivity;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.ti.TouchInterceptor;
import com.kaolafm.kradio.lib.ti.TouchInterceptorObserver;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.AppPageSlideEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.LifecycleTransformer;
import com.trello.rxlifecycle3.RxLifecycle;
import com.trello.rxlifecycle3.android.ActivityEvent;
import com.trello.rxlifecycle3.android.RxLifecycleAndroid;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.subjects.BehaviorSubject;
import io.reactivex.subjects.Subject;

/**
 * Activity基类。
 * 如果要使用EventBus只要{@link #useEventBus()}返回true。默认是不使用。<br/>
 * Activity默认是使用Fragment的，注册了Fragme的生命周期回调。如果不使用Fragment，将{@link #useFragment()}返回false即可。
 *
 * <AUTHOR>
 * @date 2018/4/13
 */

public abstract class BaseActivity<P extends IPresenter> extends ARouterBaseActivity
        implements IActivity, ActivityLifecycleable, LifecycleProvider<ActivityEvent> {

    private static final String TAG = "BaseActivity";
    private final BehaviorSubject<ActivityEvent> mLifecycleSubject = BehaviorSubject.create();

    protected P mPresenter;

    private KRadioTransStatusBarInter mKRadioTransStatusBarInter;
    private KRadioVehicleSafetyInter mKRadioVehicleSafetyInter;
    private KRadioActivityLifecyleInter mKRadioActivityLifecyleInter;

    protected View backView;
    private long startTime;
    private ConfigChangeInter configChangeInter;
    private Configuration configChangeNewConfig;

    @NonNull
    @Override
    public Observable<ActivityEvent> lifecycle() {
        return mLifecycleSubject.hide();
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindUntilEvent(ActivityEvent event) {
        return RxLifecycle.bindUntilEvent(mLifecycleSubject, event);
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindToLifecycle() {
        return RxLifecycleAndroid.bindActivity(mLifecycleSubject);
    }

    @NonNull
    @Override
    public Subject<ActivityEvent> provideLifecycleSubject() {
        return mLifecycleSubject;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.AppTheme);

        super.onCreate(savedInstanceState);
        int layoutId = 0;
        if (BuildConfig.LAYOUT_TYPE == 0) {
            layoutId = getLayoutId();
        } else if (BuildConfig.LAYOUT_TYPE == 1) {
            layoutId = getLayoutId_Tow();
            if (layoutId == 0) {
                layoutId = getLayoutId();
            }
        }

        if (layoutId != 0) {
            setContentView(layoutId);
        }
        initData();
        mPresenter = createPresenter();
        initView(savedInstanceState);

        backView = findViewById(R.id.backView);
        if (backView != null) {
            backView.setOnClickListener(this::clickBack);
            updateBackView();
        }
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        initDecorView();
    }

    public void initDecorView() {
        try {
            // android 10以后沉浸式状态栏，需要通过反射设置
            Class decorViewClazz = Class.forName("com.android.internal.policy.DecorView");
            @SuppressLint("BlockedPrivateApi")
            Field field = decorViewClazz.getDeclaredField("mSemiTransparentBarColor");
            field.setAccessible(true);
            //改为透明
            field.setInt(getWindow().getDecorView(), Color.TRANSPARENT);
            // 设置根布局fitsSystemWindows，让布局自动适应状态栏
            View contentView = findViewById(android.R.id.content);
            if (!(contentView instanceof ViewGroup)) {
                return;
            }
            ViewGroup rootView = (ViewGroup) contentView;
            if (rootView.getChildCount() == 0) {
                return;
            }
            View appRootView = rootView.getChildAt(0);
            // 获取当前屏幕的实际状态栏高度
            int actualStatusBarHeight = getCurrentScreenStatusBarHeight();
            // 获取屏幕高度来判断主副屏
            int screenHeight = ScreenUtil.getScreenHeight();
            // 根据屏幕高度动态调整适配策略
            // 主屏：1920x1080，副屏：1920x720
            float adjustFactor;
            if (screenHeight <= 720) {
                adjustFactor = 0.3f;
            } else if (screenHeight <= 1080) {
                adjustFactor = 0.15f;
            } else {
                adjustFactor = 0.12f;
            }
            int adjustedPadding = (int) (actualStatusBarHeight * adjustFactor);
            Log.d("BaseActivity", String.format("Screen: %dx%d, StatusBar: %dpx, Factor: %.2f, Padding: %dpx",
                    ScreenUtil.getScreenWidth(), screenHeight, actualStatusBarHeight, adjustFactor, adjustedPadding));
            appRootView.setPadding(
                    appRootView.getPaddingLeft(),
                    appRootView.getPaddingTop() + adjustedPadding,
                    appRootView.getPaddingRight(),
                    appRootView.getPaddingBottom()
            );
        } catch (Exception e) {
        }
    }
    
    /**
     * 获取当前屏幕的实际状态栏高度
     * 通过Window的DecorView实时获取，避免缓存问题
     */
    private int getCurrentScreenStatusBarHeight() {
        Rect rect = new Rect();
        getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
        int statusBarHeight = rect.top;
        
        // 如果获取失败，使用系统资源获取（可能有缓存）
        if (statusBarHeight <= 0) {
            statusBarHeight = ScreenUtil.getStatusBarHeight();
        }
        
        return statusBarHeight;
    }

    public void clickBack(View v) {
        if (AntiShake.check(v.getId())) {
            return;
        }
        finish();

        KRadioDialogActivityInter inter = ClazzImplUtil.getInter("KRadioDialogActivityImpl");
        if (inter != null) inter.handleBaseActivity(this);
    }

    protected void onCreateBase(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        Log.d(TAG, "-------------------"+this.getClass().getSimpleName() + " onPostCreate start -------------------");
        super.onPostCreate(savedInstanceState);
    }



    @Override
    protected void onPostResume() {
        Log.d(TAG, "-------------------"+this.getClass().getSimpleName() + " onPostResume start -------------------");
        super.onPostResume();
    }

    @Override
    public void onEnterAnimationComplete() {
        Log.d(TAG, "-------------------"+this.getClass().getSimpleName() + " onEnterAnimationComplete start -------------------");
        super.onEnterAnimationComplete();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (mPresenter != null) {
            mPresenter.start();
        }
        // 注册主题监听器
        registerThemeObserver();
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    /**
     * 创建当前view对应的Presenter
     *
     * @return
     */
    protected abstract P createPresenter();

    @Override
    public boolean useEventBus() {
        return false;
    }

    @Override
    public boolean useFragment() {
        return true;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        configChangeNewConfig = newConfig;
        synchronized (TAG) {
            showAccordingToScreen(ResUtil.getOrientation());
        }
        setReportLandOrPortrait(ResUtil.getOrientation());
        updateBackView();
        String settingsTheme = getCurrentThemeFromSettings();
        if (!android.text.TextUtils.isEmpty(settingsTheme)) {
            switchNightMode(settingsTheme);
        }
    }

    private void switchNightMode(String theme) {
        //使用换肤框架，放在flavor中处理
        if (configChangeInter == null) {
            configChangeInter = ClazzImplUtil
                    .getInter("KRadioConfigChangeImpl");
        }
        if (configChangeInter != null) {
            Log.d(TAG, "switchNightMode with theme: " + theme);
            configChangeInter.onConfigChanged(theme);
        }
    }

    protected void clearConfiguration() {
        if (configChangeInter != null) {
            configChangeInter = null;
            configChangeNewConfig = null;
        }
    }

    public void saveConfiguration() {
        if (configChangeInter == null) {
            configChangeInter = ClazzImplUtil
                    .getInter("KRadioConfigChangeImpl");
        }
        if (configChangeInter != null) {
            String theme = getCurrentThemeFromSettings();
            if (!android.text.TextUtils.isEmpty(theme)) {
                configChangeInter.saveConfiguration(theme);
            }
        }
    }

    protected void saveConfiguration(boolean forceSave) {
        if (configChangeInter == null) {
            configChangeInter = ClazzImplUtil
                    .getInter("KRadioConfigChangeImpl");
        }
        if (configChangeInter != null) {
            String theme = getCurrentThemeFromSettings();
            if (!android.text.TextUtils.isEmpty(theme)) {
                configChangeInter.saveConfiguration(theme, forceSave);
            }
        }
    }

    private boolean isAutoSkin() {
        boolean isAutoSkin = false;
        try {
            Class skinHelperClass = Class.forName("com.kaolafm.kradio.component.ui.base.skin.SkinHelper");
            Method getIsAutoSkin = skinHelperClass.getDeclaredMethod("getIsAutoSkin", Context.class);
            getIsAutoSkin.setAccessible(true);
            isAutoSkin = (boolean) getIsAutoSkin.invoke(null, this);
            getIsAutoSkin.setAccessible(false);
            Log.d(TAG + "_KRadioConfigChangeImpl", "isAutoSkin() --- normal");
        } catch (Exception | Error e) {
            Log.e(TAG + "_KRadioConfigChangeImpl", "isAutoSkin() --- error = " + e);
            isAutoSkin = false;
        }
        Log.d(TAG + "_KRadioConfigChangeImpl", "isAutoSkin() --- isAutoSkin = " + isAutoSkin);
        return isAutoSkin;
    }

    /**
     * 处理返回按钮的边距
     */
    private void updateBackView() {
        if (autoSetBackViewMarginLeft() && backView != null) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) backView.getLayoutParams();
            layoutParams.leftMargin = ScreenUtil.getGlobalBackMarginLeft(backView, ResUtil.getOrientation());
        }
    }

    protected boolean autoSetBackViewMarginLeft() {
        return true;
    }

    protected void showAccordingToScreen(int orientation) {
    }

    private void setReportLandOrPortrait(int orientation) {
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_PORTRAIT);
        } else {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_LANDSCAPE);
        }
    }

    /**
     * 是否上报页面曝光
     *
     * @return
     */
    protected boolean isReportPage() {
        return true;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isReportPage())
            startTime = System.currentTimeMillis();
        String pageId = getPageId();
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }
        startCheck(this);
        resumeCheck();
        transStatusBar();
        if (mKRadioActivityLifecyleInter != null) {
            mKRadioActivityLifecyleInter.onResume(this);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        reportPageShowEvent();
        if (mKRadioActivityLifecyleInter != null) {
            mKRadioActivityLifecyleInter.onStop(this);
        }
        // 注销主题监听器
        unregisterThemeObserver();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        Log.d(TAG, "-------------------"+this.getClass().getSimpleName() + " onWindowFocusChanged start: hasFocus=" + hasFocus+"-------------------");
        super.onWindowFocusChanged(hasFocus);
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopCheck(this);
        if (mKRadioActivityLifecyleInter != null) {
            mKRadioActivityLifecyleInter.onPause(this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.destroy();
        }
        mPresenter = null;
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        if (!isReportPage()) {
            return;
        }
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPage(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
//    @Override
//    public void finish() {
//        super.finish();
//        if (isNeedAnimation) {
//            overridePendingTransition(0, R.anim.exit_default_transition_animation);
//        }
//    }


    /**
     * 获取pageid
     *
     * @return
     */
    public String getPageId() {
        return Constants.BLANK_STR;
    }

    public void transStatusBar() {
        if (mKRadioTransStatusBarInter != null) {
            mKRadioTransStatusBarInter.changeStatusBarColor(this, R.color.transparent_color);
        }
    }

    public void changeViewLayoutForStatusBar(View view) {
        if (mKRadioTransStatusBarInter != null) {
            mKRadioTransStatusBarInter.changeViewLayoutForStatusBar(view, 0);
        }
    }

    public boolean canChangeViewLayoutForStatusBar() {
        if (mKRadioTransStatusBarInter != null) {
            return mKRadioTransStatusBarInter.canChangeViewLayoutForStatusBar();
        }
        return false;
    }

    /***************************************************************************************************************/

    @Override
    public boolean onTouchEvent(MotionEvent event) {

        return super.onTouchEvent(event);
    }

    float startX = 0, startY = 0, endX = 0, endY = 0;
    private final float SLIDE_ANGLE = 45;

    /**
     * 判定滑动方向
     *
     * @return
     */
    private String getDirection() {
        String direction = "";
        float xDiff = Math.abs(endX - startX);
        float yDiff = Math.abs(endY - startY);
        double squareRoot = Math.sqrt(xDiff * xDiff + yDiff * yDiff);
        //滑动的角度
        int yAngle = Math.round((float) (Math.asin(yDiff / squareRoot) / Math.PI * 180));
        int xAngle = Math.round((float) (Math.asin(xDiff / squareRoot) / Math.PI * 180));
        boolean isMeetSlidingYAngle = yAngle > SLIDE_ANGLE;//滑动角度是否大于45du
        boolean isMeetSlidingXAngle = xAngle > SLIDE_ANGLE;//滑动角度是否大于45du
        boolean isSlideUp = endY < startY && isMeetSlidingYAngle;
        boolean isSlideDown = endY > startY && isMeetSlidingYAngle;
        boolean isSlideLeft = endX < startX && isMeetSlidingXAngle;
        boolean isSlideRight = endX > startX && isMeetSlidingXAngle;
//                滑动方向 上滑 up 下滑 down 左滑 left 右滑right
        if (isSlideUp) {
            Log.d("cai-page-slide", "向上滑动");
            direction = "up";
        } else if (isSlideDown) {
            Log.d("cai-page-slide", "向下滑动");
            direction = "down";
        } else if (isSlideLeft) {
            Log.d("cai-page-slide", "向左边滑动");
            direction = "left";
        } else if (isSlideRight) {
            Log.d("cai-page-slide", "向右边滑动");
            direction = "right";
        }
        return direction;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = ev.getX();
                startY = ev.getY();
                break;
            case MotionEvent.ACTION_UP:
                endX = ev.getX();
                endY = ev.getY();
                Log.d("cai-page-slide", "start:X-" + startX + ",Y-" + startY + "   end:X-" + endX + ",Y-" + endY);
                String startXY = startX + "," + startY;
                String endXY = endX + "," + endY;
                String direction = getDirection();
                if (!TextUtils.isEmpty(direction)) {
                    reportPageSlideEvent(direction, startXY, endXY);
                }
                break;
        }
        if (mTouchInterceptor != null) {
            boolean dispatchTouchEvent = mTouchInterceptor.dispatchTouchEvent(this, ev);
            if (dispatchTouchEvent) {
                return true;
            } else {
                return super.dispatchTouchEvent(ev);
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 上报页面滑动
     *
     * @param direction     滑动方向 上滑 up 下滑 down 作滑 left 右滑right
     * @param startPosition 滑动起始坐标
     * @param endPosition   滑动结束坐标
     */
    private void reportPageSlideEvent(String direction, String startPosition, String endPosition) {
        AppPageSlideEvent pageSlideEvent = new AppPageSlideEvent();
        pageSlideEvent.setPageid(ReportParameterManager.getInstance().getPage());
        pageSlideEvent.setDirection(direction);
        pageSlideEvent.setResolution(ScreenUtil.getScreeSize());
        pageSlideEvent.setStartposition(startPosition);
        pageSlideEvent.setEndposition(endPosition);
        ReportHelper.getInstance().addEvent(pageSlideEvent);

    }

    private TouchInterceptor mTouchInterceptor;
    private Map<LifecycleOwner, TouchInterceptorObserver> tiMap = new HashMap<>();

    public void setTouchInterceptor(TouchInterceptor touchInterceptor) {
        mTouchInterceptor = touchInterceptor;
    }


    public void registerTouchInterceptor(LifecycleOwner owner, List<View> views) {
        TouchInterceptorObserver touchInterceptorObserver = tiMap.get(owner);
        if (touchInterceptorObserver == null) {
            touchInterceptorObserver = new TouchInterceptorObserver(owner, (BaseActivity) this, views);
            tiMap.put(owner, touchInterceptorObserver);
        }
        owner.getLifecycle().addObserver(touchInterceptorObserver);
    }


    public void unregisterTouchInterceptor(LifecycleOwner owner) {
        TouchInterceptorObserver remove = tiMap.remove(owner);
        if (remove != null) {
            remove.discard();
        }
    }

    /**
     * ###################################check vehicle safety start###############################################
     */
    protected boolean startCheck(Object... args) {
        if (mKRadioVehicleSafetyInter != null) {
            return mKRadioVehicleSafetyInter.startCheck(args);
        }
        return false;
    }

    protected boolean stopCheck(Object... args) {
        if (mKRadioVehicleSafetyInter != null) {
            return mKRadioVehicleSafetyInter.stopCheck(args);
        }
        return false;
    }

    public void registerVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyInter != null) {
            mKRadioVehicleSafetyInter.registerVehicleSafetyCheckCallback(kRadioVehicleSafetyCallback);
        }
    }

    public void unregisterVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyInter != null) {
            mKRadioVehicleSafetyInter.unregisterVehicleSafetyCheckCallback(kRadioVehicleSafetyCallback);
        }
    }

    public boolean resumeCheck(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyInter != null) {
            return mKRadioVehicleSafetyInter.resumeCheck(kRadioVehicleSafetyCallback);
        }
        return true;
    }

    public boolean resumeCheck() {
        if (mKRadioVehicleSafetyInter != null) {
            return mKRadioVehicleSafetyInter.resumeCheck();
        }
        return true;
    }
    /**
     * ###################################check vehicle safety end###############################################
     */

    // 主题监听相关
    private android.database.ContentObserver mThemeObserver;
    private boolean isThemeObserverRegistered = false;

    /**
     * 注册主题监听器
     */
    private void registerThemeObserver() {
        if (isThemeObserverRegistered) {
            return;
        }

        try {
            mThemeObserver = new android.database.ContentObserver(new android.os.Handler(android.os.Looper.getMainLooper())) {
                @Override
                public void onChange(boolean selfChange, android.net.Uri uri) {
                    super.onChange(selfChange, uri);
                    Log.d(TAG, "Settings theme changed, uri = " + uri);
                    String newTheme = getCurrentThemeFromSettings();
                    Log.d(TAG, "New theme from settings: " + newTheme);
                    if (!android.text.TextUtils.isEmpty(newTheme)) {
                        switchNightMode(newTheme);
                    }
                }
            };

            // 车厂提供的主题键名
            final String KEY_THEME_TYPE = "android.car.THEME_TYPE";
            getContentResolver().registerContentObserver(
                    android.provider.Settings.System.getUriFor(KEY_THEME_TYPE),
                    true,
                    mThemeObserver
            );

            isThemeObserverRegistered = true;
            Log.d(TAG, "Theme observer registered in " + this.getClass().getSimpleName());

            // 初始化时读取一次当前主题
            String currentTheme = getCurrentThemeFromSettings();
            if (!android.text.TextUtils.isEmpty(currentTheme)) {
                Log.d(TAG, "Initial theme from settings: " + currentTheme);
                saveConfiguration(true);
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to register theme observer", e);
        }
    }

    /**
     * 注销主题监听器
     */
    private void unregisterThemeObserver() {
        if (!isThemeObserverRegistered || mThemeObserver == null) {
            return;
        }
        try {
            getContentResolver().unregisterContentObserver(mThemeObserver);
            isThemeObserverRegistered = false;
            mThemeObserver = null;
            Log.d(TAG, "Theme observer unregistered in " + this.getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to unregister theme observer", e);
        }
    }

    /**
     * 从Settings数据库读取当前主题
     */
    protected String getCurrentThemeFromSettings() {
        try {
            // 车厂提供的主题键名
            final String KEY_THEME_TYPE = "android.car.THEME_TYPE";
            String theme = android.provider.Settings.System.getString(getContentResolver(), KEY_THEME_TYPE);
            Log.d(TAG, "getCurrentThemeFromSettings: " + theme);
            return theme;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get theme from settings", e);
            return null;
        }
    }
}
