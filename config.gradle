import com.kaolafm.gradle.plugin.Util

ext {
    majorVersionCode_comprehensive = 30012
    majorVersionName_comprehensive = "3.01.2"
    majorVersionCode_online = 10001
    majorVersionName_online = "1.0.1"

    def majorVersionCode = getVersionCode()
    def majorVersionName = getVersionName()

    def readVersion = Util.readLocalProperties(rootProject, "local.sdkVersion")
    def readMinVersion = Util.readLocalProperties(rootProject, "local.minVersion")
    def sdkVersion = (readVersion == null ? 29 : readVersion.toInteger())
    def minVersion = (readMinVersion == null ? 26 : readMinVersion.toInteger())

    android = [
            compileSdkVersion: sdkVersion,
            buildToolsVersion: "29.0.2",
            minSdkVersion    : minVersion,
            targetSdkVersion : sdkVersion,
            versionCode      : majorVersionCode,
            versionName      : majorVersionName,
            javaSourceVersion: JavaVersion.VERSION_1_8,
            javaTargetVersion: JavaVersion.VERSION_1_8
    ]
    version = [
            retrofitSdkVersion    : "2.9.0",
            rxlifecycle3SdkVersion: "3.1.0",
            canarySdkVersion      : "2.11",
            glideVersion          : "4.13.2",
            dagger2Version        : "2.25.4",
            skin_support          : "4.0.5",
    ]
    dependencies = [
            // android
            "junit"                    : "junit:junit:4.12",
            "lifecycle-runtime"        : "androidx.lifecycle:lifecycle-runtime:2.5.0",
            "lifecycle-extensions"     : "androidx.lifecycle:lifecycle-extensions:2.2.0",
            "persistence-room-runtime" : "androidx.room:room-runtime:2.4.1",
            "persistence-room-compiler": "androidx.room:room-compiler:2.4.1",
            "persistence-room-rxjava2" : "androidx.room:room-rxjava2:2.4.1",
            "support-v4"               : 'androidx.legacy:legacy-support-v4:1.0.0',
            "appcompat-v7"             : 'androidx.appcompat:appcompat:1.2.0',
            "constraint"               : 'androidx.constraintlayout:constraintlayout:1.1.3',
            "design"                   : 'com.google.android.material:material:1.4.0',
            "cardview"                 : 'androidx.cardview:cardview:1.0.0',
            "recyclerview-v7"          : 'androidx.recyclerview:recyclerview:1.0.0',
            "annotations"              : "androidx.annotation:annotation:1.3.0",
            "mediaCompat"              : "androidx.media:media:1.0.0",
            "multidex"                 : 'androidx.multidex:multidex:2.0.1',
            // google
            "gson"                     : "com.google.code.gson:gson:2.10.1",
            "zxing"                    : "com.google.zxing:core:3.5.1",
            "dagger2"                  : "com.google.dagger:dagger:${version["dagger2Version"]}",
            "dagger2-compiler"         : "com.google.dagger:dagger-compiler:${version["dagger2Version"]}",
            "dagger2-android"          : "com.google.dagger:dagger-android:${version["dagger2Version"]}",
            "dagger2-android-support"  : "com.google.dagger:dagger-android-support:${version["dagger2Version"]}",
            "dagger2-android-processor": "com.google.dagger:dagger-android-processor:${version["dagger2Version"]}",

            // squareup
            "canary-debug"             : "com.squareup.leakcanary:leakcanary-android:${version["canarySdkVersion"]}",
            "retrofit2"                : "com.squareup.retrofit2:retrofit:${version["retrofitSdkVersion"]}",
            "retrofit2-gson"           : "com.squareup.retrofit2:converter-gson:${version["retrofitSdkVersion"]}",
            "retrofit2-rxjava2"        : "com.squareup.retrofit2:adapter-rxjava2:${version["retrofitSdkVersion"]}",
            // rx
            "rxandroid2"               : "io.reactivex.rxjava3:rxandroid:3.1.6",
            "rxjava2"                  : "io.reactivex.rxjava3:rxjava:3.1.6",
            "rxlifecycle2"             : "com.trello.rxlifecycle3:rxlifecycle:${version["rxlifecycle3SdkVersion"]}",
            "rxlifecycle2-components"  : "com.trello.rxlifecycle3:rxlifecycle-components:${version["rxlifecycle3SdkVersion"]}",

            // greenrobot
            "eventbus"                 : "org.greenrobot:eventbus:3.1.1",
            "greenDao"                 : "org.greenrobot:greendao:3.3.0",
            // tencent
            "bugly_crashreport"        : "com.tencent.bugly:crashreport:latest.release",
            "bugly_nativecrashreport"  : "com.tencent.bugly:nativecrashreport:latest.release",

            // alibaba
            "fastjson"                 : "com.alibaba:fastjson:1.2.83",
            // "arouter"                  : "com.github.jadepeakpoet.ARouter:arouter-api:1.0.3",
            "arouter-compiler"         : "com.github.jadepeakpoet.ARouter:arouter-compiler:1.0.3",
            // github
            "glide"                    : "com.github.bumptech.glide:glide:${version["glideVersion"]}",
            "glide-compiler"           : "com.github.bumptech.glide:compiler:${version["glideVersion"]}",
            // jakewharton
            //
            "skeleton"                 : "com.ethanhua:skeleton:1.1.2",
            "xpopup"                   : "com.github.li-xiaojun:XPopup:2.7.5",
            "tablayout"                : "io.github.h07000223:flycoTabLayout:3.0.0",
            "SmartRefreshHorizontal"   : "com.scwang.smartrefresh:SmartRefreshHorizontal:1.1.2-x",
            "lottie"                   : "com.airbnb.android:lottie:2.8.0",
            "rebound"                  : "com.facebook.rebound:rebound:0.3.8",
            "webpdecoder"              : "com.github.zjupure:webpdecoder:2.3.${version["glideVersion"]}",//webp解码
            "apng"                     : "com.github.penfeizhou.android.animation:apng:2.25.0",//apng动画解码
            "shimmerlayout"            : "io.supercharge:shimmerlayout:2.1.0",
            //skin
            "skin-support"             : "skin.support:skin-support:${version["skin_support"]}",
            "skin-support-appcompat"   : "skin.support:skin-support-appcompat:${version["skin_support"]}",
            "skin-support-design"      : "skin.support:skin-support-design:${version["skin_support"]}",
            "mmkv"                     : "com.tencent:mmkv:1.0.19",
            "relinker"                 : "com.getkeepsafe.relinker:relinker:1.3.1",
            "skin-support-cardview"    : "skin.support:skin-support-cardview:${version["skin_support"]}",
            "skin-support-constraint"  : "skin.support:skin-support-constraint-layout:${version["skin_support"]}",
            // own
            "OpensdkDebug"             : "OpensdkDebug-3.0.11-SNAPSHOT",
    ]
    jpush = [
            "jpush-pkgname": "com.edog.car",
            "jpush-appkey" : "4c15e06f4f25f3ecbc38a3c8",
            "jpush-channel": "developer-default"
    ]
    amap = [
            "amap-appkey": "f32731c0b606bbaa7750794910ffba68"
    ]

    //定义当前"flavor"给其他gradle脚本文件使用，引用方式"rootProject.ext.flavor"
    //取"local.properties"中的"SINGLE_CHANNEL"，避免重复配置
    flavor = [
            "channel": Util.readLocalProperties(rootProject, "SINGLE_CHANNEL")
    ]
}


/**
 * 获取版本号
 * @return
 */
def getVersionCode() {
//    return Util.getVersionCode(rootProject)
    def majorVersion = null
    def patterns = Util.getBuildPattern(rootProject)
    if (patterns.contains("comprehensive")) {
        majorVersion = majorVersionCode_comprehensive
    }
    if (patterns.contains("online")) {
        majorVersion = majorVersionCode_online
    }
    return majorVersion
}

/**
 * 获取版本名称
 * @return
 */
def getVersionName() {
//    return Util.getVersionName(rootProject)
    def majorVersion = null
    def patterns = Util.getBuildPattern(rootProject)
    if (patterns.contains("comprehensive")) {
        majorVersion = majorVersionName_comprehensive
    }
    if (patterns.contains("online")) {
        majorVersion = majorVersionName_online
    }
    return majorVersion
}


